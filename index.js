// Import required Discord.js classes
const {
  Client,
  GatewayIntentBits,
  Partials,
  Collection,
  REST,
  Routes,
  Events,
  PermissionsBitField,
  MessageFlags,
  ButtonBuilder,
  ButtonStyle,
  ActionRowBuilder,
  UserSelectMenuBuilder
} = require('discord.js');
const fs = require('fs');
const path = require('path');
const config = require('./config.js');

// Import handlers
const reactionRoleHandler = require('./utils/reactionRoleHandler.js');

// Initialize client with ALL required intents
const client = new Client({
  intents: [
    GatewayIntentBits.Guilds,
    GatewayIntentBits.GuildMessages,
    GatewayIntentBits.MessageContent,
    GatewayIntentBits.GuildMembers,
    GatewayIntentBits.DirectMessages
  ],
  partials: [Partials.Channel, Partials.Message]
});

// Create a collection for commands
client.commands = new Collection();

// Add debugging for errors
process.on('unhandledRejection', error => {
  console.error('Unhandled promise rejection:', error);
});

// Load command files
const commandsPath = path.join(__dirname, 'commands');
const commandFiles = fs.readdirSync(commandsPath).filter(file => file.endsWith('.js'));

// Command registration array for REST API
const commands = [];

// Load each command into the client.commands collection
for (const file of commandFiles) {
  const filePath = path.join(commandsPath, file);
  const command = require(filePath);

  // Check if the command has the required properties
  if ('data' in command && 'execute' in command) {
    client.commands.set(command.data.name, command);
    commands.push(command.data.toJSON());
    console.log(`Loaded command: ${command.data.name}`);
  } else {
    console.warn(`Warning: The command at ${filePath} is missing required "data" or "execute" property.`);
  }
}

// Bot ready event
client.once(Events.ClientReady, async readyClient => {
  console.log(`Logged in as ${readyClient.user.tag}`);
  console.log('Bot is ready!');

  // Register slash commands globally when the bot starts
  try {
    console.log('Started refreshing application (/) commands.');

    const rest = new REST({ version: '10' }).setToken(config.token);

    // Register commands globally
    await rest.put(
      Routes.applicationCommands(readyClient.user.id),
      { body: commands }
    );

    console.log('Successfully registered application commands globally.');
  } catch (error) {
    console.error('Error registering commands:', error);
  }

  // List the servers the bot is in
  console.log(`Bot is in ${client.guilds.cache.size} servers`);
  client.guilds.cache.forEach(guild => {
    console.log(`- ${guild.name} (${guild.id})`);
  });
});

// Message handling
client.on(Events.MessageCreate, async message => {
  console.log(`[MESSAGE] ${message.author.tag}: ${message.content}`);

  // Ignore messages from bots
  if (message.author.bot) return;

  // Simple command to test if the bot is responsive
  if (message.content === '!ping') {
    console.log('Ping command received!');
    try {
      await message.reply('Pong!');
      console.log('Replied to ping');
    } catch (error) {
      console.error('Error replying to ping:', error);
    }
  }

  // Channel creation command (legacy text command)
  if (message.content.startsWith('!create-channel')) {
    console.log('Create channel command received!');
    const channelName = message.content.split(' ')[1];

    if (!channelName) {
      console.log('No channel name provided');
      return message.reply('Please provide a channel name!');
    }

    console.log(`Attempting to create channel: ${channelName}`);
    try {
      const newChannel = await message.guild.channels.create({
        name: channelName,
        type: 0 // Text channel
      });

      console.log(`Channel created: ${newChannel.name}`);
      await message.reply(`Successfully created channel: ${newChannel.name}`);
    } catch (error) {
      console.error(`Error creating channel:`, error);
      await message.reply(`Failed to create channel: ${error.message}`);
    }
  }
});

// Interaction handling for slash commands
client.on(Events.InteractionCreate, async interaction => {
  // Handling slash commands
  if (interaction.isChatInputCommand()) {
    console.log(`[INTERACTION] Command used: ${interaction.commandName}`);

    // Get the command from the collection
    const command = client.commands.get(interaction.commandName);

    // If command doesn't exist
    if (!command) {
      console.error(`No command matching ${interaction.commandName} was found.`);
      return interaction.reply({
        content: 'This command is not properly implemented.',
        flags: MessageFlags.Ephemeral
      });
    }

    // Execute the command
    try {
      await command.execute(interaction);
      console.log(`Executed command: ${interaction.commandName}`);
    } catch (error) {
      console.error(`Error executing ${interaction.commandName}:`);
      console.error(error);

      try {
        // If the interaction has already been replied to or deferred
        if (interaction.replied || interaction.deferred) {
          await interaction.followUp({
            content: `There was an error executing this command: ${error.message}`,
            flags: MessageFlags.Ephemeral
          }).catch(followUpError => {
            console.error('Error sending followUp after command error:', followUpError);
          });
        } else {
          await interaction.reply({
            content: `There was an error executing this command: ${error.message}`,
            flags: MessageFlags.Ephemeral
          }).catch(replyError => {
            console.error('Error sending reply after command error:', replyError);
          });
        }
      } catch (responseError) {
        console.error('Error responding to interaction after command error:', responseError);
      }
    }
  }

  // Handling button interactions
  else if (interaction.isButton()) {
    console.log(`[BUTTON] Received button click: ${interaction.customId}`);

    try {
      // Handle reaction role button interactions
      if (interaction.customId.startsWith('reaction_role_')) {
        const reactionRoleHandler = require('./utils/reactionRoleHandler');
        await reactionRoleHandler.handleReactionRoleButton(interaction);
        return;
      }

      // Handle style button interactions (for role addition workflow)
      if (interaction.customId.startsWith('style_')) {
        const setupCommand = client.commands.get('setup-reaction-roles');
        if (setupCommand && setupCommand.handleButtonStyleSelection) {
          await setupCommand.handleButtonStyleSelection(interaction);
          return;
        }
      }

      // Handle dashboard button interactions
      if (interaction.customId.startsWith('dashboard_') ||
          interaction.customId.startsWith('role_') ||
          interaction.customId.startsWith('select_role_') ||
          interaction.customId.startsWith('role_selection_')) {
        const setupCommand = client.commands.get('setup-reaction-roles');
        if (setupCommand && setupCommand.handleDashboardButton) {
          await setupCommand.handleDashboardButton(interaction);
          return;
        }
      }

      const bulkManagerCommand = client.commands.get('bulkmanager');

      if (interaction.customId === 'refreshBulkManagerMenu' || interaction.customId === 'backToBulkManager') {
        // Show the main menu again
        await bulkManagerCommand.showMainMenu(interaction, true).catch(error => {
          console.error('Error showing main menu:', error);

          // Try to respond with a simple message if the menu fails
          if (!interaction.replied && !interaction.deferred) {
            interaction.reply({
              content: 'There was an error refreshing the menu. Please try the command again.',
              flags: MessageFlags.Ephemeral
            }).catch(replyError => {
              console.error('Error sending reply after menu error:', replyError);
            });
          } else {
            interaction.followUp({
              content: 'There was an error refreshing the menu. Please try the command again.',
              flags: MessageFlags.Ephemeral
            }).catch(followUpError => {
              console.error('Error sending followUp after menu error:', followUpError);
            });
          }
        });
      }

      // Handle delete skipped roles button
      if (interaction.customId.startsWith('deleteSkippedRoles_')) {
        const dataKey = interaction.customId.replace('deleteSkippedRoles_', '');

        // Check if user has permission to delete roles
        if (!interaction.member.permissions.has(PermissionsBitField.Flags.ManageRoles)) {
          return await interaction.reply({
            content: '❌ You need the "Manage Roles" permission to delete roles.',
            flags: MessageFlags.Ephemeral
          });
        }

        // Get stored data
        if (!global.skippedRolesData || !global.skippedRolesData.has(dataKey)) {
          return await interaction.reply({
            content: '❌ This action has expired. Please run the operation again.',
            flags: MessageFlags.Ephemeral
          });
        }

        const data = global.skippedRolesData.get(dataKey);

        // Check if data is too old (5 minutes)
        if (Date.now() - data.timestamp > 5 * 60 * 1000) {
          global.skippedRolesData.delete(dataKey);
          return await interaction.reply({
            content: '❌ This action has expired. Please run the operation again.',
            flags: MessageFlags.Ephemeral
          });
        }

        // Show confirmation modal
        const modal = new ModalBuilder()
          .setCustomId(`confirmDeleteSkippedRoles_${dataKey}`)
          .setTitle('Confirm Role Deletion');

        const confirmationInput = new TextInputBuilder()
          .setCustomId('confirmation')
          .setLabel('Type "DELETE" to confirm role deletion')
          .setStyle(TextInputStyle.Short)
          .setPlaceholder('DELETE')
          .setRequired(true)
          .setMaxLength(6);

        const roleListInput = new TextInputBuilder()
          .setCustomId('roleList')
          .setLabel('Roles to be deleted:')
          .setStyle(TextInputStyle.Paragraph)
          .setValue(data.roleNames.join(', '))
          .setRequired(false);

        const firstRow = new ActionRowBuilder().addComponents(confirmationInput);
        const secondRow = new ActionRowBuilder().addComponents(roleListInput);
        modal.addComponents(firstRow, secondRow);

        await interaction.showModal(modal);
      }

      // Handle keep skipped roles button
      if (interaction.customId.startsWith('keepSkippedRoles_')) {
        const dataKey = interaction.customId.replace('keepSkippedRoles_', '');

        // Get stored data
        if (!global.skippedRolesData || !global.skippedRolesData.has(dataKey)) {
          return await interaction.reply({
            content: '❌ This action has expired.',
            flags: MessageFlags.Ephemeral
          });
        }

        const data = global.skippedRolesData.get(dataKey);

        // Clean up the stored data
        global.skippedRolesData.delete(dataKey);

        // Respond with confirmation
        await interaction.reply({
          content: `✅ **Roles Kept Successfully**\n\nThe following existing roles will remain unchanged:\n${data.roleNames.join(', ')}\n\nNo further action is required.`,
          flags: MessageFlags.Ephemeral
        });
      }

      // Handle role choice buttons (use, create, replace)
      if (interaction.customId.startsWith('roleChoice_')) {
        const parts = interaction.customId.split('_');
        const action = parts[1]; // use, create, or replace
        const choiceId = parts.slice(2).join('_'); // rejoin the rest as choiceId

        // Get stored choice data
        const choiceData = bulkManagerCommand.getRoleChoiceData(choiceId);
        if (!choiceData) {
          return await interaction.reply({
            content: '❌ This action has expired. Please restart the operation.',
            flags: MessageFlags.Ephemeral
          });
        }

        await interaction.deferReply({ flags: MessageFlags.Ephemeral });

        try {
          const currentChoice = choiceData.pendingChoices[choiceData.currentIndex];
          let roleToUse = null;
          let actionTaken = '';

          switch (action) {
            case 'use':
              roleToUse = currentChoice.existingRole;
              actionTaken = `Used existing role "${currentChoice.existingRole.name}"`;
              break;

            case 'create':
              try {
                roleToUse = await interaction.guild.roles.create({
                  name: currentChoice.targetRoleName,
                  ...currentChoice.roleOptions
                });
                choiceData.successCount++;
                actionTaken = `Created new role "${roleToUse.name}"`;
              } catch (error) {
                choiceData.failedRoles.push(`${currentChoice.targetRoleName} (${error.message})`);
                actionTaken = `Failed to create role "${currentChoice.targetRoleName}"`;
              }
              break;

            case 'replace':
              try {
                // Delete existing role first
                await currentChoice.existingRole.delete(`Replaced by new role "${currentChoice.targetRoleName}"`);

                // Create new role
                roleToUse = await interaction.guild.roles.create({
                  name: currentChoice.targetRoleName,
                  ...currentChoice.roleOptions
                });
                choiceData.successCount++;
                actionTaken = `Replaced "${currentChoice.existingRole.name}" with "${roleToUse.name}"`;
              } catch (error) {
                choiceData.failedRoles.push(`${currentChoice.targetRoleName} (Replace failed: ${error.message})`);
                actionTaken = `Failed to replace role "${currentChoice.existingRole.name}"`;
              }
              break;
          }

          // Move to next choice or finish
          choiceData.currentIndex++;

          if (choiceData.currentIndex < choiceData.pendingChoices.length) {
            // Process next choice
            const nextChoice = choiceData.pendingChoices[choiceData.currentIndex];
            bulkManagerCommand.storeRoleChoiceData(choiceId, choiceData);

            const selectionInterface = bulkManagerCommand.createRoleSelectionInterface(
              nextChoice.channelName,
              nextChoice.targetRoleName,
              nextChoice.existingRole,
              choiceId
            );

            await interaction.followUp({
              content: `✅ ${actionTaken}\n\n**Next Conflict** (${choiceData.currentIndex + 1} of ${choiceData.pendingChoices.length}):`,
              ...selectionInterface,
              flags: MessageFlags.Ephemeral
            });
          } else {
            // All choices processed, show final results
            bulkManagerCommand.cleanupRoleChoiceData(choiceId);

            let response = `✅ **All Role Conflicts Resolved**\n\n`;
            response += `Final Results:\n`;
            response += `• Created ${choiceData.successCount} roles successfully\n`;

            if (choiceData.skippedRoles && choiceData.skippedRoles.length > 0) {
              response += `• Skipped ${choiceData.skippedRoles.length} existing roles\n`;
            }

            if (choiceData.failedRoles && choiceData.failedRoles.length > 0) {
              response += `• Failed ${choiceData.failedRoles.length} roles\n`;
            }

            response += `\nLast action: ${actionTaken}`;

            await interaction.followUp({
              content: response,
              flags: MessageFlags.Ephemeral
            });
          }
        } catch (error) {
          console.error('Error processing role choice:', error);
          await interaction.followUp({
            content: `❌ An error occurred: ${error.message}`,
            flags: MessageFlags.Ephemeral
          });
        }
      }
    } catch (error) {
      console.error('Error handling button interaction:', error);

      try {
        if (!interaction.replied && !interaction.deferred) {
          await interaction.reply({
            content: `An error occurred: ${error.message}`,
            flags: MessageFlags.Ephemeral
          });
        } else {
          await interaction.followUp({
            content: `An error occurred: ${error.message}`,
            flags: MessageFlags.Ephemeral
          });
        }
      } catch (responseError) {
        console.error('Error responding to button interaction after error:', responseError);
      }
    }
  }

  // Handling select menu interactions
  else if (interaction.isStringSelectMenu()) {
    console.log(`[SELECT MENU] Received selection: ${interaction.customId}`);

    // Handle dashboard select menu interactions
    if (interaction.customId.startsWith('select_')) {
      const setupCommand = client.commands.get('setup-reaction-roles');
      if (setupCommand && setupCommand.handleDashboardSelect) {
        await setupCommand.handleDashboardSelect(interaction);
        return;
      }
    }

    const bulkManagerCommand = client.commands.get('bulkmanager');

    // Handle bulkManagerSelect menu (string select)
    if (interaction.customId === 'bulkManagerSelect') {
      const selectedValue = interaction.values[0];

      try {
        switch (selectedValue) {
          case 'bulkRemoveRoles':
            // Show modal for removing roles from all members
            const bulkRemoveRolesModal = bulkManagerCommand.createBulkRemoveRolesModal();
            await interaction.showModal(bulkRemoveRolesModal);
            break;

          case 'bulkUserRoleManagement':
            // Show modal for bulk user role management
            const bulkUserRoleManagementModal = bulkManagerCommand.createBulkUserRoleManagementModal();
            await interaction.showModal(bulkUserRoleManagementModal);
            break;

          // Removed individual user role management cases in favor of the bulk option

          case 'bulkDeleteRoles':
            // Show modal for deleting roles
            const bulkDeleteRolesModal = bulkManagerCommand.createBulkDeleteRolesModal();
            await interaction.showModal(bulkDeleteRolesModal);
            break;

          case 'bulkDeleteRolesByPattern':
            // Show modal for deleting roles by pattern
            const bulkDeleteRolesByPatternModal = bulkManagerCommand.createBulkDeleteRolesByPatternModal();
            await interaction.showModal(bulkDeleteRolesByPatternModal);
            break;

          case 'bulkDeleteChannels':
            // Show modal for deleting channels
            const bulkDeleteChannelsModal = bulkManagerCommand.createBulkDeleteChannelsModal();
            await interaction.showModal(bulkDeleteChannelsModal);
            break;

          case 'bulkDeleteChannelsByCategory':
            // Show modal for deleting channels by category
            const bulkDeleteChannelsByCategoryModal = bulkManagerCommand.createBulkDeleteChannelsByCategoryModal();
            await interaction.showModal(bulkDeleteChannelsByCategoryModal);
            break;

          case 'bulkDeleteChannelsByPattern':
            // Show modal for deleting channels by pattern
            const bulkDeleteChannelsByPatternModal = bulkManagerCommand.createBulkDeleteChannelsByPatternModal();
            await interaction.showModal(bulkDeleteChannelsByPatternModal);
            break;

          case 'createChannels':
            // Show modal for creating channels
            const createChannelsModal = bulkManagerCommand.createChannelsModal();
            await interaction.showModal(createChannelsModal);
            break;

          case 'createPrivateChannels':
            // Show modal for creating private channels
            const createPrivateChannelsModal = bulkManagerCommand.createPrivateChannelsModal();
            await interaction.showModal(createPrivateChannelsModal);
            break;

          case 'createRoles':
            // Show modal for creating roles
            const createRolesModal = bulkManagerCommand.createRolesModal();
            await interaction.showModal(createRolesModal);
            break;

          default:
            await interaction.reply({
              content: 'Unknown operation selected.',
              flags: MessageFlags.Ephemeral
            });
        }
      } catch (error) {
        console.error('Error handling select menu interaction:', error);
        if (!interaction.replied) {
          await interaction.reply({
            content: `An error occurred: ${error.message}`,
            flags: MessageFlags.Ephemeral
          });
        } else {
          await interaction.followUp({
            content: `An error occurred: ${error.message}`,
            flags: MessageFlags.Ephemeral
          });
        }
      }
    }
  }

  // Handling modal submissions for our commands
  else if (interaction.isModalSubmit()) {
    console.log(`[MODAL] Received modal submission: ${interaction.customId}`);

    try {
      // Handle dashboard modal interactions
      if (interaction.customId.startsWith('modal_')) {
        const setupCommand = client.commands.get('setup-reaction-roles');
        if (setupCommand && setupCommand.handleDashboardModal) {
          await setupCommand.handleDashboardModal(interaction);
          return;
        }
      }



      // Handle createChannelsModal submission
      if (interaction.customId === 'createChannelsModal') {
      const channelNames = interaction.fields.getTextInputValue('channelNamesInput').split('\n');
      const channelType = interaction.fields.getTextInputValue('channelTypeInput').toLowerCase();
      const categoryName = interaction.fields.getTextInputValue('categoryInput');

      // Acknowledge the interaction
      await interaction.deferReply({ flags: MessageFlags.Ephemeral });

      try {
        // Find or create category if specified
        let categoryChannel = null;
        if (categoryName) {
          categoryChannel = interaction.guild.channels.cache.find(
            channel => channel.type === 4 && channel.name.toLowerCase() === categoryName.toLowerCase()
          );

          if (!categoryChannel) {
            categoryChannel = await interaction.guild.channels.create({
              name: categoryName,
              type: 4, // Category type
            });
          }
        }

        // Create each channel
        let successCount = 0;
        const failedChannels = [];

        for (const name of channelNames) {
          if (name.trim()) {
            try {
              const channelOptions = {
                name: name.trim(),
                type: channelType === 'voice' ? 2 : 0, // 0 for text, 2 for voice
                parent: categoryChannel ? categoryChannel.id : null
              };

              const newChannel = await interaction.guild.channels.create(channelOptions);
              successCount++;
              console.log(`Created channel: ${newChannel.name}`);
            } catch (error) {
              console.error(`Failed to create channel ${name}:`, error);
              failedChannels.push(`${name} (${error.message})`);
            }
          }
        }

        // Respond with results
        let response = `Created ${successCount} channels successfully.`;
        if (failedChannels.length > 0) {
          response += `\nFailed to create ${failedChannels.length} channels: ${failedChannels.join(', ')}`;
        }

        await interaction.followUp({
          content: response,
          flags: MessageFlags.Ephemeral
        });
      } catch (error) {
        console.error('Error processing createChannelsModal:', error);
        await interaction.followUp({
          content: `An error occurred: ${error.message}`,
          flags: MessageFlags.Ephemeral
        });
      }
    }

    // Handle bulkUserRoleManagementModal submission
    else if (interaction.customId === 'bulkUserRoleManagementModal') {
      const userNames = interaction.fields.getTextInputValue('userNamesInput').split('\n');
      const roleNames = interaction.fields.getTextInputValue('roleNamesInput').split('\n');
      const actionType = interaction.fields.getTextInputValue('actionTypeInput').toLowerCase();

      // Acknowledge the interaction
      await interaction.deferReply({ flags: MessageFlags.Ephemeral });

      try {
        // First message to user
        await interaction.followUp({
          content: `Processing bulk user role ${actionType} request. This may take some time...`,
          flags: MessageFlags.Ephemeral
        });

        // Get the bot's highest role position
        const botMember = await interaction.guild.members.fetch(interaction.client.user.id);
        const botHighestRole = botMember.roles.highest;

        console.log(`Bot's highest role: ${botHighestRole.name} (Position: ${botHighestRole.position})`);

        // Initialize counters and arrays for tracking
        let userSuccessCount = 0;
        let roleSuccessCount = 0;
        let totalOperations = 0;
        const notFoundUsers = [];
        const notFoundRoles = [];
        const failedOperations = [];
        const processedUsers = [];
        const processedRoles = [];

        // First, resolve all role names/IDs to actual roles
        const resolvedRoles = [];
        for (const roleName of roleNames) {
          if (roleName.trim()) {
            // Try to find by ID first
            let role;
            if (/^\d+$/.test(roleName.trim())) {
              // It's a numeric ID
              role = interaction.guild.roles.cache.get(roleName.trim());
            }

            // If not found by ID, try by name
            if (!role) {
              role = interaction.guild.roles.cache.find(r => r.name.toLowerCase() === roleName.trim().toLowerCase());
            }

            if (role) {
              // Check if the bot can manage this role
              if (role.position >= botHighestRole.position) {
                console.log(`Role is higher than bot's highest role and cannot be managed: ${role.name}`);
                failedOperations.push(`Role "${role.name}" is higher in hierarchy than bot's roles`);
              } else {
                resolvedRoles.push(role);
                processedRoles.push(role.name);
              }
            } else {
              console.log(`Role not found: ${roleName.trim()}`);
              notFoundRoles.push(roleName.trim());
            }
          }
        }

        // If no valid roles were found, exit early
        if (resolvedRoles.length === 0) {
          return await interaction.followUp({
            content: `No valid roles were found to ${actionType}. Please check the role names/IDs and try again.`,
            flags: MessageFlags.Ephemeral
          });
        }

        // Update the user
        await interaction.followUp({
          content: `Found ${resolvedRoles.length} valid roles: ${processedRoles.join(', ')}`,
          flags: MessageFlags.Ephemeral
        });

        // Now, resolve all user names/IDs to actual members
        for (const userName of userNames) {
          if (userName.trim()) {
            try {
              // Try to find by ID first
              let member;
              if (/^\d+$/.test(userName.trim())) {
                // It's a numeric ID
                member = await interaction.guild.members.fetch(userName.trim()).catch(() => null);
              }

              // If not found by ID, try by username
              if (!member) {
                const members = await interaction.guild.members.fetch();
                member = members.find(m =>
                  m.user.username.toLowerCase() === userName.trim().toLowerCase() ||
                  m.displayName.toLowerCase() === userName.trim().toLowerCase() ||
                  (m.nickname && m.nickname.toLowerCase() === userName.trim().toLowerCase())
                );
              }

              if (member) {
                processedUsers.push(member.user.tag);

                // Process each role for this user
                let userRoleSuccessCount = 0;

                for (const role of resolvedRoles) {
                  totalOperations++;

                  try {
                    if (actionType === 'add') {
                      // Check if user already has the role
                      if (member.roles.cache.has(role.id)) {
                        console.log(`User ${member.user.tag} already has role ${role.name}`);
                        continue;
                      }

                      // Add the role
                      await member.roles.add(role, `Bulk role addition requested by ${interaction.user.tag}`);
                      console.log(`Added role ${role.name} to ${member.user.tag}`);
                      userRoleSuccessCount++;
                      roleSuccessCount++;
                    } else {
                      // Check if user has the role
                      if (!member.roles.cache.has(role.id)) {
                        console.log(`User ${member.user.tag} doesn't have role ${role.name}`);
                        continue;
                      }

                      // Remove the role
                      await member.roles.remove(role, `Bulk role removal requested by ${interaction.user.tag}`);
                      console.log(`Removed role ${role.name} from ${member.user.tag}`);
                      userRoleSuccessCount++;
                      roleSuccessCount++;
                    }

                    // Add a small delay to avoid rate limiting
                    await new Promise(resolve => setTimeout(resolve, 300));
                  } catch (error) {
                    console.error(`Failed to ${actionType} role ${role.name} ${actionType === 'add' ? 'to' : 'from'} ${member.user.tag}:`, error);
                    failedOperations.push(`${role.name} ${actionType === 'add' ? 'to' : 'from'} ${member.user.tag} (${error.message})`);
                  }
                }

                if (userRoleSuccessCount > 0) {
                  userSuccessCount++;
                }

                // Notify about progress
                await interaction.followUp({
                  content: `Processed user ${member.user.tag}: ${userRoleSuccessCount} roles ${actionType === 'add' ? 'added' : 'removed'} successfully`,
                  flags: MessageFlags.Ephemeral
                });
              } else {
                console.log(`User not found: ${userName.trim()}`);
                notFoundUsers.push(userName.trim());
              }
            } catch (error) {
              console.error(`Error processing user ${userName.trim()}:`, error);
              failedOperations.push(`User ${userName.trim()} (${error.message})`);
            }
          }
        }

        // Create a back button
        const backButton = new ButtonBuilder()
          .setCustomId('backToBulkManager')
          .setLabel('Back to Menu')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('⬅️');

        const backButtonRow = new ActionRowBuilder().addComponents(backButton);

        // Respond with results
        let response = `Bulk user role ${actionType} operation completed.\n`;
        response += `• Processed ${processedUsers.length} users: ${processedUsers.join(', ')}\n`;
        response += `• Processed ${processedRoles.length} roles: ${processedRoles.join(', ')}\n`;
        response += `• Successfully ${actionType === 'add' ? 'added' : 'removed'} ${roleSuccessCount} roles out of ${totalOperations} operations\n`;

        if (notFoundUsers.length > 0) {
          response += `• Couldn't find ${notFoundUsers.length} users: ${notFoundUsers.join(', ')}\n`;
        }

        if (notFoundRoles.length > 0) {
          response += `• Couldn't find ${notFoundRoles.length} roles: ${notFoundRoles.join(', ')}\n`;
        }

        if (failedOperations.length > 0) {
          response += `• Failed operations: ${failedOperations.join(', ')}`;
        }

        await interaction.followUp({
          content: response,
          components: [backButtonRow],
          flags: MessageFlags.Ephemeral
        });
      } catch (error) {
        console.error('Error processing bulkUserRoleManagementModal:', error);
        await interaction.followUp({
          content: `An error occurred: ${error.message}`,
          flags: MessageFlags.Ephemeral
        });
      }
    }

    // Handle createRolesModal submission
    else if (interaction.customId === 'createRolesModal') {
      const roleNames = interaction.fields.getTextInputValue('roleNamesInput').split('\n');
      const roleColor = interaction.fields.getTextInputValue('roleColorInput');
      const roleVisibility = interaction.fields.getTextInputValue('roleVisibilityInput').toLowerCase();

      // Acknowledge the interaction
      await interaction.deferReply({ flags: MessageFlags.Ephemeral });

      try {
        // Set role options
        const roleOptions = {};
        if (roleColor && roleColor.match(/^#[0-9A-F]{6}$/i)) {
          roleOptions.color = roleColor;
        }
        roleOptions.hoist = roleVisibility === 'yes';

        // Create each role
        let successCount = 0;
        const failedRoles = [];

        for (const name of roleNames) {
          if (name.trim()) {
            try {
              const newRole = await interaction.guild.roles.create({
                name: name.trim(),
                ...roleOptions
              });
              successCount++;
              console.log(`Created role: ${newRole.name}`);
            } catch (error) {
              console.error(`Failed to create role ${name}:`, error);
              failedRoles.push(`${name} (${error.message})`);
            }
          }
        }

        // Respond with results
        let response = `Created ${successCount} roles successfully.`;
        if (failedRoles.length > 0) {
          response += `\nFailed to create ${failedRoles.length} roles: ${failedRoles.join(', ')}`;
        }

        await interaction.followUp({
          content: response,
          flags: MessageFlags.Ephemeral
        });
      } catch (error) {
        console.error('Error processing createRolesModal:', error);
        await interaction.followUp({
          content: `An error occurred: ${error.message}`,
          flags: MessageFlags.Ephemeral
        });
      }
    }

    // Handle createPrivateChannelsModal submission
    else if (interaction.customId === 'createPrivateChannelsModal') {
      const channelNames = interaction.fields.getTextInputValue('channelNamesInput').split('\n');
      const channelType = interaction.fields.getTextInputValue('channelTypeInput').toLowerCase();
      const categoryName = interaction.fields.getTextInputValue('categoryInput');
      const roleColor = interaction.fields.getTextInputValue('roleColorInput');

      // Acknowledge the interaction
      await interaction.deferReply({ flags: MessageFlags.Ephemeral });

      try {
        // Find or create category if specified
        let categoryChannel = null;
        if (categoryName) {
          categoryChannel = interaction.guild.channels.cache.find(
            channel => channel.type === 4 && channel.name.toLowerCase() === categoryName.toLowerCase()
          );

          if (!categoryChannel) {
            categoryChannel = await interaction.guild.channels.create({
              name: categoryName,
              type: 4, // Category type
            });
          }
        }

        // Set role options
        const roleOptions = {};
        if (roleColor && roleColor.match(/^#[0-9A-F]{6}$/i)) {
          roleOptions.color = roleColor;
        }

        // Create each channel and matching role
        let successCount = 0;
        const failedItems = [];

        for (const name of channelNames) {
          if (name.trim()) {
            try {
              // First create the role
              const newRole = await interaction.guild.roles.create({
                name: name.trim(),
                ...roleOptions
              });

              console.log(`Created role: ${newRole.name}`);

              // Get @everyone role ID
              const everyoneRole = interaction.guild.roles.everyone;

              // Set permissions based on channel type
              const isVoiceChannel = channelType === 'voice';
              let allowPermissions;

              if (isVoiceChannel) {
                // For voice channels
                allowPermissions = [
                  PermissionsBitField.Flags.ViewChannel,
                  PermissionsBitField.Flags.Connect,
                  PermissionsBitField.Flags.Speak
                ];
              } else {
                // For text channels
                allowPermissions = [
                  PermissionsBitField.Flags.ViewChannel,
                  PermissionsBitField.Flags.SendMessages,
                  PermissionsBitField.Flags.ReadMessageHistory
                ];
              }

              // Create the channel with proper permissions
              const channelOptions = {
                name: name.trim(),
                type: channelType === 'voice' ? 2 : 0, // 0 for text, 2 for voice
                parent: categoryChannel ? categoryChannel.id : null,
                permissionOverwrites: [
                  {
                    id: everyoneRole.id,
                    deny: [PermissionsBitField.Flags.ViewChannel] // Deny view access to everyone
                  },
                  {
                    id: newRole.id,
                    allow: allowPermissions // Allow appropriate permissions for the role
                  }
                ]
              };

              const newChannel = await interaction.guild.channels.create(channelOptions);

              console.log(`Created private ${isVoiceChannel ? 'voice' : 'text'} channel: ${newChannel.name}`);
              successCount++;
            } catch (error) {
              console.error(`Failed to create private channel/role for ${name}:`, error);
              failedItems.push(`${name} (${error.message})`);
            }
          }
        }

        // Respond with results
        let response = `Created ${successCount} private channels with matching roles successfully.`;
        if (skippedRoles.length > 0) {
          response += `\nUsed ${skippedRoles.length} existing roles: ${skippedRoles.join(', ')}`;
        }
        if (failedItems.length > 0) {
          response += `\nFailed to create ${failedItems.length} items: ${failedItems.join(', ')}`;
        }

        // Create components array for the response
        const components = [];

        // Add delete button if there are skipped roles
        if (skippedRoles.length > 0) {
          const deleteButton = new ButtonBuilder()
            .setCustomId(`deleteSkippedRoles_${interaction.user.id}_${Date.now()}`)
            .setLabel('Delete Existing Roles')
            .setStyle(ButtonStyle.Danger)
            .setEmoji('🗑️');

          const actionRow = new ActionRowBuilder().addComponents(deleteButton);
          components.push(actionRow);

          // Store skipped roles data for later use
          if (!global.skippedRolesData) {
            global.skippedRolesData = new Map();
          }
          const dataKey = `${interaction.user.id}_${Date.now()}`;
          global.skippedRolesData.set(dataKey, {
            roleNames: skippedRoles,
            guildId: interaction.guild.id,
            timestamp: Date.now()
          });
        }

        await interaction.followUp({
          content: response,
          components: components,
          flags: MessageFlags.Ephemeral
        });
      } catch (error) {
        console.error('Error processing createPrivateChannelsModal:', error);
        await interaction.followUp({
          content: `An error occurred: ${error.message}`,
          flags: MessageFlags.Ephemeral
        });
      }
    }

    // Handle bulkRemoveRolesModal submission
    else if (interaction.customId === 'bulkRemoveRolesModal') {
      const roleNames = interaction.fields.getTextInputValue('roleNamesInput').split('\n');

      // Acknowledge the interaction
      await interaction.deferReply({ flags: MessageFlags.Ephemeral });

      try {
        // First message to user
        await interaction.followUp({
          content: `Processing bulk role removal request. This may take some time...`,
          flags: MessageFlags.Ephemeral
        });

        let successCount = 0;
        const failedRoles = [];
        const notFoundRoles = [];

        // Process each role
        for (const name of roleNames) {
          if (name.trim()) {
            // Find the role by name
            const role = interaction.guild.roles.cache.find(r => r.name.toLowerCase() === name.trim().toLowerCase());

            if (!role) {
              console.log(`Role not found: ${name.trim()}`);
              notFoundRoles.push(name.trim());
              continue;
            }

            try {
              // Fetch all guild members first to ensure we have the latest data
              await interaction.followUp({
                content: `Fetching members with role "${role.name}"...`,
                flags: MessageFlags.Ephemeral
              });

              // Fetch all members with this role
              const fetchedMembers = await interaction.guild.members.fetch();
              const membersWithRole = fetchedMembers.filter(member => member.roles.cache.has(role.id));

              await interaction.followUp({
                content: `Found ${membersWithRole.size} members with role "${role.name}". Removing role...`,
                flags: MessageFlags.Ephemeral
              });

              // Remove the role from each member
              let memberSuccessCount = 0;
              for (const [memberId, member] of membersWithRole) {
                try {
                  await member.roles.remove(role);
                  memberSuccessCount++;

                  // Add a small delay to avoid rate limiting
                  await new Promise(resolve => setTimeout(resolve, 100));
                } catch (memberError) {
                  console.error(`Failed to remove role ${role.name} from member ${member.user.tag}:`, memberError);
                }
              }

              console.log(`Removed role ${role.name} from ${memberSuccessCount} members`);
              successCount++;
            } catch (error) {
              console.error(`Failed to process role ${name.trim()}:`, error);
              failedRoles.push(`${name.trim()} (${error.message})`);
            }
          }
        }

        // Respond with results
        let response = `Removed ${successCount} roles from members successfully.`;
        if (notFoundRoles.length > 0) {
          response += `\nCouldn't find ${notFoundRoles.length} roles: ${notFoundRoles.join(', ')}`;
        }
        if (failedRoles.length > 0) {
          response += `\nFailed to process ${failedRoles.length} roles: ${failedRoles.join(', ')}`;
        }

        // Create a back button
        const backButton = new ButtonBuilder()
          .setCustomId('backToBulkManager')
          .setLabel('Back to Menu')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('⬅️');

        const backButtonRow = new ActionRowBuilder().addComponents(backButton);

        await interaction.followUp({
          content: response,
          components: [backButtonRow],
          flags: MessageFlags.Ephemeral
        });
      } catch (error) {
        console.error('Error processing bulkRemoveRolesModal:', error);
        await interaction.followUp({
          content: `An error occurred: ${error.message}`,
          flags: MessageFlags.Ephemeral
        });
      }
    }

    // Handle bulkDeleteRolesModal submission
    else if (interaction.customId === 'bulkDeleteRolesModal') {
      const roleNames = interaction.fields.getTextInputValue('roleNamesInput').split('\n');

      // Acknowledge the interaction
      await interaction.deferReply({ flags: MessageFlags.Ephemeral });

      try {
        // First message to user
        await interaction.followUp({
          content: `Processing bulk role deletion request. This may take some time...`,
          flags: MessageFlags.Ephemeral
        });

        let successCount = 0;
        const failedRoles = [];
        const notFoundRoles = [];

        // Get the bot's highest role position
        const botMember = await interaction.guild.members.fetch(interaction.client.user.id);
        const botHighestRole = botMember.roles.highest;

        console.log(`Bot's highest role: ${botHighestRole.name} (Position: ${botHighestRole.position})`);

        // Process each role
        for (const name of roleNames) {
          if (name.trim()) {
            // Find the role by name
            const role = interaction.guild.roles.cache.find(r => r.name.toLowerCase() === name.trim().toLowerCase());

            if (!role) {
              console.log(`Role not found: ${name.trim()}`);
              notFoundRoles.push(name.trim());
              continue;
            }

            console.log(`Processing role: ${role.name} (ID: ${role.id}, Position: ${role.position}, Managed: ${role.managed})`);

            // Check if the role can be deleted
            if (role.managed) {
              console.log(`Role is managed by an integration and cannot be deleted: ${name.trim()}`);
              failedRoles.push(`${name.trim()} (Role is managed by an integration)`);
              continue;
            }

            if (role.position >= botHighestRole.position) {
              console.log(`Role is higher than bot's highest role and cannot be deleted: ${name.trim()}`);
              failedRoles.push(`${name.trim()} (Role is higher in hierarchy than bot's roles)`);
              continue;
            }

            try {
              // Notify about the role being deleted
              await interaction.followUp({
                content: `Attempting to delete role: ${role.name}...`,
                flags: MessageFlags.Ephemeral
              });

              // Delete the role
              await role.delete(`Bulk role deletion requested by ${interaction.user.tag}`);
              console.log(`Deleted role: ${name.trim()}`);
              successCount++;

              // Add a small delay to avoid rate limiting
              await new Promise(resolve => setTimeout(resolve, 500));
            } catch (error) {
              console.error(`Failed to delete role ${name.trim()}:`, error);
              failedRoles.push(`${name.trim()} (${error.message})`);
            }
          }
        }

        // Respond with results
        let response = `Deleted ${successCount} roles successfully.`;
        if (notFoundRoles.length > 0) {
          response += `\nCouldn't find ${notFoundRoles.length} roles: ${notFoundRoles.join(', ')}`;
        }
        if (failedRoles.length > 0) {
          response += `\nFailed to delete ${failedRoles.length} roles: ${failedRoles.join(', ')}`;
        }

        // Create a back button
        const backButton = new ButtonBuilder()
          .setCustomId('backToBulkManager')
          .setLabel('Back to Menu')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('⬅️');

        const backButtonRow = new ActionRowBuilder().addComponents(backButton);

        await interaction.followUp({
          content: response,
          components: [backButtonRow],
          flags: MessageFlags.Ephemeral
        });
      } catch (error) {
        console.error('Error processing bulkDeleteRolesModal:', error);
        await interaction.followUp({
          content: `An error occurred: ${error.message}`,
          flags: MessageFlags.Ephemeral
        });
      }
    }

    // Handle bulkDeleteChannelsModal submission
    else if (interaction.customId === 'bulkDeleteChannelsModal') {
      const channelNames = interaction.fields.getTextInputValue('channelNamesInput').split('\n');

      // Acknowledge the interaction
      await interaction.deferReply({ flags: MessageFlags.Ephemeral });

      try {
        // First message to user
        await interaction.followUp({
          content: `Processing bulk channel deletion request. This may take some time...`,
          flags: MessageFlags.Ephemeral
        });

        let successCount = 0;
        const failedChannels = [];
        const notFoundChannels = [];

        // Process each channel
        for (const name of channelNames) {
          if (name.trim()) {
            // Find the channel by name (case insensitive)
            const channel = interaction.guild.channels.cache.find(
              c => c.name.toLowerCase() === name.trim().toLowerCase()
            );

            if (!channel) {
              console.log(`Channel not found: ${name.trim()}`);
              notFoundChannels.push(name.trim());
              continue;
            }

            console.log(`Processing channel: ${channel.name} (ID: ${channel.id}, Type: ${channel.type})`);

            try {
              // Notify about the channel being deleted
              await interaction.followUp({
                content: `Attempting to delete channel: ${channel.name}...`,
                flags: MessageFlags.Ephemeral
              });

              // Delete the channel
              await channel.delete(`Bulk channel deletion requested by ${interaction.user.tag}`);
              console.log(`Deleted channel: ${name.trim()}`);
              successCount++;

              // Add a small delay to avoid rate limiting
              await new Promise(resolve => setTimeout(resolve, 500));
            } catch (error) {
              console.error(`Failed to delete channel ${name.trim()}:`, error);
              failedChannels.push(`${name.trim()} (${error.message})`);
            }
          }
        }

        // Respond with results
        let response = `Deleted ${successCount} channels successfully.`;
        if (notFoundChannels.length > 0) {
          response += `\nCouldn't find ${notFoundChannels.length} channels: ${notFoundChannels.join(', ')}`;
        }
        if (failedChannels.length > 0) {
          response += `\nFailed to delete ${failedChannels.length} channels: ${failedChannels.join(', ')}`;
        }

        // Create a back button
        const backButton = new ButtonBuilder()
          .setCustomId('backToBulkManager')
          .setLabel('Back to Menu')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('⬅️');

        const backButtonRow = new ActionRowBuilder().addComponents(backButton);

        await interaction.followUp({
          content: response,
          components: [backButtonRow],
          flags: MessageFlags.Ephemeral
        });
      } catch (error) {
        console.error('Error processing bulkDeleteChannelsModal:', error);
        await interaction.followUp({
          content: `An error occurred: ${error.message}`,
          flags: MessageFlags.Ephemeral
        });
      }
    }

    // Handle bulkRemoveUserRolesModal submission
    else if (interaction.customId.startsWith('bulkRemoveUserRolesModal-')) {
      // Extract the user ID from the customId
      const userId = interaction.customId.split('-')[1];
      const roleNames = interaction.fields.getTextInputValue('roleNamesInput').split('\n');

      // Acknowledge the interaction
      await interaction.deferReply({ flags: MessageFlags.Ephemeral });

      try {
        // First message to user
        await interaction.followUp({
          content: `Processing bulk role removal request for user. This may take some time...`,
          flags: MessageFlags.Ephemeral
        });

        // Fetch the target member
        const targetMember = await interaction.guild.members.fetch(userId);

        if (!targetMember) {
          return await interaction.followUp({
            content: `Error: Could not find the user in this server.`,
            flags: MessageFlags.Ephemeral
          });
        }

        console.log(`Processing roles for user: ${targetMember.user.tag} (ID: ${targetMember.id})`);

        // Get the bot's highest role position
        const botMember = await interaction.guild.members.fetch(interaction.client.user.id);
        const botHighestRole = botMember.roles.highest;

        console.log(`Bot's highest role: ${botHighestRole.name} (Position: ${botHighestRole.position})`);

        let successCount = 0;
        const failedRoles = [];
        const notFoundRoles = [];

        // Process each role
        for (const name of roleNames) {
          if (name.trim()) {
            // Find the role by name
            const role = interaction.guild.roles.cache.find(r => r.name.toLowerCase() === name.trim().toLowerCase());

            if (!role) {
              console.log(`Role not found: ${name.trim()}`);
              notFoundRoles.push(name.trim());
              continue;
            }

            console.log(`Processing role: ${role.name} (ID: ${role.id}, Position: ${role.position})`);

            // Check if the user has this role
            if (!targetMember.roles.cache.has(role.id)) {
              console.log(`User doesn't have role: ${name.trim()}`);
              continue;
            }

            // Check if the bot can manage this role
            if (role.position >= botHighestRole.position) {
              console.log(`Role is higher than bot's highest role and cannot be managed: ${name.trim()}`);
              failedRoles.push(`${name.trim()} (Role is higher in hierarchy than bot's roles)`);
              continue;
            }

            try {
              // Notify about the role being removed
              await interaction.followUp({
                content: `Removing role "${role.name}" from ${targetMember.user.username}...`,
                flags: MessageFlags.Ephemeral
              });

              // Remove the role from the user
              await targetMember.roles.remove(role, `Bulk role removal requested by ${interaction.user.tag}`);
              console.log(`Removed role ${role.name} from ${targetMember.user.tag}`);
              successCount++;

              // Add a small delay to avoid rate limiting
              await new Promise(resolve => setTimeout(resolve, 300));
            } catch (error) {
              console.error(`Failed to remove role ${name.trim()} from user:`, error);
              failedRoles.push(`${name.trim()} (${error.message})`);
            }
          }
        }

        // Respond with results
        let response = `Removed ${successCount} roles from ${targetMember.user.tag} successfully.`;
        if (notFoundRoles.length > 0) {
          response += `\nCouldn't find ${notFoundRoles.length} roles: ${notFoundRoles.join(', ')}`;
        }
        if (failedRoles.length > 0) {
          response += `\nFailed to remove ${failedRoles.length} roles: ${failedRoles.join(', ')}`;
        }

        // Create a back button
        const backButton = new ButtonBuilder()
          .setCustomId('backToBulkManager')
          .setLabel('Back to Menu')
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('⬅️');

        const backButtonRow = new ActionRowBuilder().addComponents(backButton);

        await interaction.followUp({
          content: response,
          components: [backButtonRow],
          flags: MessageFlags.Ephemeral
        });
      } catch (error) {
        console.error('Error processing bulkRemoveUserRolesModal:', error);
        await interaction.followUp({
          content: `An error occurred: ${error.message}`,
          flags: MessageFlags.Ephemeral
        });
      }
    }

    // Handle bulkRemoveUserRolesByIdModal submission
    else if (interaction.customId.startsWith('bulkRemoveUserRolesByIdModal-')) {
      // Extract the user ID from the customId
      const userId = interaction.customId.split('-')[1];
      const roleIds = interaction.fields.getTextInputValue('roleIdsInput').split('\n');

      // Acknowledge the interaction
      await interaction.deferReply({ flags: MessageFlags.Ephemeral });

      try {
        // Fetch the target member
        const targetMember = await interaction.guild.members.fetch(userId);

        if (!targetMember) {
          return await interaction.followUp({
            content: `Error: Could not find the user in this server.`,
            flags: MessageFlags.Ephemeral
          });
        }

        let successCount = 0;
        const failedRoles = [];
        const notFoundRoles = [];

        // Process each role ID
        for (const id of roleIds) {
          if (id.trim()) {
            // Find the role by ID
            const role = interaction.guild.roles.cache.get(id.trim());

            if (!role) {
              console.log(`Role not found with ID: ${id.trim()}`);
              notFoundRoles.push(id.trim());
              continue;
            }

            // Check if the user has this role
            if (!targetMember.roles.cache.has(role.id)) {
              console.log(`User doesn't have role: ${role.name} (${id.trim()})`);
              continue;
            }

            try {
              // Remove the role from the user
              await targetMember.roles.remove(role, `Bulk role removal requested by ${interaction.user.tag}`);
              console.log(`Removed role ${role.name} from ${targetMember.user.tag}`);
              successCount++;
            } catch (error) {
              console.error(`Failed to remove role ${role.name} (${id.trim()}) from user:`, error);
              failedRoles.push(`${role.name} (${error.message})`);
            }
          }
        }

        // Respond with results
        let response = `Removed ${successCount} roles from ${targetMember.user.tag} successfully.`;
        if (notFoundRoles.length > 0) {
          response += `\nCouldn't find ${notFoundRoles.length} roles with IDs: ${notFoundRoles.join(', ')}`;
        }
        if (failedRoles.length > 0) {
          response += `\nFailed to remove ${failedRoles.length} roles: ${failedRoles.join(', ')}`;
        }

        await interaction.followUp({
          content: response,
          flags: MessageFlags.Ephemeral
        });
      } catch (error) {
        console.error('Error processing bulkRemoveUserRolesByIdModal:', error);
        await interaction.followUp({
          content: `An error occurred: ${error.message}`,
          flags: MessageFlags.Ephemeral
        });
      }
    }

    // Handle bulkDeleteChannelsByCategoryModal submission
    else if (interaction.customId === 'bulkDeleteChannelsByCategoryModal') {
      const categoryName = interaction.fields.getTextInputValue('categoryNameInput');
      const deleteCategoryInput = interaction.fields.getTextInputValue('deleteCategoryInput').toLowerCase();
      const deleteCategory = deleteCategoryInput === 'yes';

      // Acknowledge the interaction
      await interaction.deferReply({ flags: MessageFlags.Ephemeral });

      try {
        // Find the category by name
        const category = interaction.guild.channels.cache.find(
          channel => channel.type === 4 && channel.name.toLowerCase() === categoryName.toLowerCase()
        );

        if (!category) {
          return await interaction.followUp({
            content: `Error: Could not find a category named "${categoryName}" in this server.`,
            flags: MessageFlags.Ephemeral
          });
        }

        // Get all channels in this category
        const channelsInCategory = interaction.guild.channels.cache.filter(
          channel => channel.parentId === category.id
        );

        if (channelsInCategory.size === 0) {
          let response = `The category "${categoryName}" doesn't contain any channels.`;

          // Delete the category if requested
          if (deleteCategory) {
            try {
              await category.delete(`Bulk category deletion requested by ${interaction.user.tag}`);
              response += `\nThe category itself has been deleted.`;
            } catch (error) {
              console.error(`Failed to delete category ${categoryName}:`, error);
              response += `\nFailed to delete the category: ${error.message}`;
            }
          }

          return await interaction.followUp({
            content: response,
            flags: MessageFlags.Ephemeral
          });
        }

        let successCount = 0;
        const failedChannels = [];

        // Delete each channel in the category
        for (const [channelId, channel] of channelsInCategory) {
          try {
            await channel.delete(`Bulk channel deletion requested by ${interaction.user.tag}`);
            console.log(`Deleted channel: ${channel.name}`);
            successCount++;
          } catch (error) {
            console.error(`Failed to delete channel ${channel.name}:`, error);
            failedChannels.push(`${channel.name} (${error.message})`);
          }
        }

        // Delete the category if requested
        let categoryDeleted = false;
        let categoryError = null;

        if (deleteCategory) {
          try {
            await category.delete(`Bulk category deletion requested by ${interaction.user.tag}`);
            categoryDeleted = true;
          } catch (error) {
            console.error(`Failed to delete category ${categoryName}:`, error);
            categoryError = error.message;
          }
        }

        // Respond with results
        let response = `Deleted ${successCount} channels from category "${categoryName}" successfully.`;
        if (failedChannels.length > 0) {
          response += `\nFailed to delete ${failedChannels.length} channels: ${failedChannels.join(', ')}`;
        }

        if (deleteCategory) {
          if (categoryDeleted) {
            response += `\nThe category itself has been deleted.`;
          } else {
            response += `\nFailed to delete the category: ${categoryError}`;
          }
        }

        await interaction.followUp({
          content: response,
          flags: MessageFlags.Ephemeral
        });
      } catch (error) {
        console.error('Error processing bulkDeleteChannelsByCategoryModal:', error);
        await interaction.followUp({
          content: `An error occurred: ${error.message}`,
          flags: MessageFlags.Ephemeral
        });
      }
    }

    // Handle bulkDeleteRolesByPatternModal submission
    else if (interaction.customId === 'bulkDeleteRolesByPatternModal') {
      const pattern = interaction.fields.getTextInputValue('patternInput');
      const confirmation = interaction.fields.getTextInputValue('confirmationInput');

      // Acknowledge the interaction
      await interaction.deferReply({ flags: MessageFlags.Ephemeral });

      try {
        // First message to user
        await interaction.followUp({
          content: `Processing bulk role deletion by pattern request. This may take some time...`,
          flags: MessageFlags.Ephemeral
        });

        // Check confirmation
        if (confirmation.toLowerCase() !== 'confirm') {
          return await interaction.followUp({
            content: `Operation cancelled. You must type "confirm" to proceed with bulk role deletion.`,
            flags: MessageFlags.Ephemeral
          });
        }

        // Get the bot's highest role position
        const botMember = await interaction.guild.members.fetch(interaction.client.user.id);
        const botHighestRole = botMember.roles.highest;

        console.log(`Bot's highest role: ${botHighestRole.name} (Position: ${botHighestRole.position})`);

        // Find all roles that match the pattern
        const matchingRoles = interaction.guild.roles.cache.filter(
          role => role.name.includes(pattern) && !role.managed && role.id !== interaction.guild.id
        );

        if (matchingRoles.size === 0) {
          return await interaction.followUp({
            content: `No roles found matching the pattern "${pattern}".`,
            flags: MessageFlags.Ephemeral
          });
        }

        console.log(`Found ${matchingRoles.size} roles matching pattern "${pattern}"`);

        // Filter roles that can be deleted (not managed and lower than bot's highest role)
        const deletableRoles = matchingRoles.filter(role =>
          !role.managed && role.position < botHighestRole.position
        );

        const nonDeletableRoles = matchingRoles.filter(role =>
          role.managed || role.position >= botHighestRole.position
        );

        console.log(`${deletableRoles.size} roles can be deleted, ${nonDeletableRoles.size} roles cannot be deleted`);

        // Confirm with the user
        let confirmMessage = `Found ${matchingRoles.size} roles matching the pattern "${pattern}":\n${matchingRoles.map(r => r.name).join(', ')}`;

        if (nonDeletableRoles.size > 0) {
          confirmMessage += `\n\n⚠️ ${nonDeletableRoles.size} roles cannot be deleted due to permissions or because they are managed by integrations:\n${nonDeletableRoles.map(r => r.name).join(', ')}`;
        }

        confirmMessage += `\n\nAttempting to delete ${deletableRoles.size} roles...`;

        await interaction.followUp({
          content: confirmMessage,
          flags: MessageFlags.Ephemeral
        });

        let successCount = 0;
        const failedRoles = [];

        // Delete each matching role that can be deleted
        for (const [roleId, role] of deletableRoles) {
          try {
            // Notify about the role being deleted
            await interaction.followUp({
              content: `Attempting to delete role: ${role.name}...`,
              flags: MessageFlags.Ephemeral
            });

            await role.delete(`Bulk role deletion by pattern requested by ${interaction.user.tag}`);
            console.log(`Deleted role: ${role.name}`);
            successCount++;

            // Add a small delay to avoid rate limiting
            await new Promise(resolve => setTimeout(resolve, 500));
          } catch (error) {
            console.error(`Failed to delete role ${role.name}:`, error);
            failedRoles.push(`${role.name} (${error.message})`);
          }
        }

        // Respond with results
        let response = `Deleted ${successCount} roles matching the pattern "${pattern}" successfully.`;
        if (failedRoles.length > 0) {
          response += `\nFailed to delete ${failedRoles.length} roles: ${failedRoles.join(', ')}`;
        }

        await interaction.followUp({
          content: response,
          flags: MessageFlags.Ephemeral
        });
      } catch (error) {
        console.error('Error processing bulkDeleteRolesByPatternModal:', error);
        await interaction.followUp({
          content: `An error occurred: ${error.message}`,
          flags: MessageFlags.Ephemeral
        });
      }
    }

    // Handle bulkDeleteChannelsByPatternModal submission
    else if (interaction.customId === 'bulkDeleteChannelsByPatternModal') {
      const pattern = interaction.fields.getTextInputValue('patternInput');
      const confirmation = interaction.fields.getTextInputValue('confirmationInput');

      // Acknowledge the interaction
      await interaction.deferReply({ flags: MessageFlags.Ephemeral });

      try {
        // First message to user
        await interaction.followUp({
          content: `Processing bulk channel deletion by pattern request. This may take some time...`,
          flags: MessageFlags.Ephemeral
        });

        // Check confirmation
        if (confirmation.toLowerCase() !== 'confirm') {
          return await interaction.followUp({
            content: `Operation cancelled. You must type "confirm" to proceed with bulk channel deletion.`,
            flags: MessageFlags.Ephemeral
          });
        }

        // Find all channels that match the pattern (excluding categories)
        const matchingChannels = interaction.guild.channels.cache.filter(
          channel => channel.type !== 4 && channel.name.includes(pattern)
        );

        if (matchingChannels.size === 0) {
          return await interaction.followUp({
            content: `No channels found matching the pattern "${pattern}".`,
            flags: MessageFlags.Ephemeral
          });
        }

        console.log(`Found ${matchingChannels.size} channels matching pattern "${pattern}"`);

        // Confirm with the user
        await interaction.followUp({
          content: `Found ${matchingChannels.size} channels matching the pattern "${pattern}":\n${matchingChannels.map(c => c.name).join(', ')}\n\nDeleting channels...`,
          flags: MessageFlags.Ephemeral
        });

        let successCount = 0;
        const failedChannels = [];

        // Delete each matching channel
        for (const [channelId, channel] of matchingChannels) {
          try {
            // Notify about the channel being deleted
            await interaction.followUp({
              content: `Attempting to delete channel: ${channel.name}...`,
              flags: MessageFlags.Ephemeral
            });

            await channel.delete(`Bulk channel deletion by pattern requested by ${interaction.user.tag}`);
            console.log(`Deleted channel: ${channel.name}`);
            successCount++;

            // Add a small delay to avoid rate limiting
            await new Promise(resolve => setTimeout(resolve, 500));
          } catch (error) {
            console.error(`Failed to delete channel ${channel.name}:`, error);
            failedChannels.push(`${channel.name} (${error.message})`);
          }
        }

        // Respond with results
        let response = `Deleted ${successCount} channels matching the pattern "${pattern}" successfully.`;
        if (failedChannels.length > 0) {
          response += `\nFailed to delete ${failedChannels.length} channels: ${failedChannels.join(', ')}`;
        }

        await interaction.followUp({
          content: response,
          flags: MessageFlags.Ephemeral
        });
      } catch (error) {
        console.error('Error processing bulkDeleteChannelsByPatternModal:', error);
        await interaction.followUp({
          content: `An error occurred: ${error.message}`,
          flags: MessageFlags.Ephemeral
        });
      }
    }
    } catch (error) {
      console.error('Error handling modal interaction:', error);
      try {
        if (!interaction.replied && !interaction.deferred) {
          await interaction.reply({
            content: `An error occurred: ${error.message}`,
            flags: MessageFlags.Ephemeral
          });
        } else {
          await interaction.followUp({
            content: `An error occurred: ${error.message}`,
            flags: MessageFlags.Ephemeral
          });
        }
      } catch (responseError) {
        console.error('Error responding to modal interaction after error:', responseError);
      }
    }
  }

  // Removed individual user select menu handler in favor of the bulk user role management
});

// Cleanup old skipped roles data every 10 minutes
setInterval(() => {
  if (global.skippedRolesData) {
    const now = Date.now();
    const expiredKeys = [];

    for (const [key, data] of global.skippedRolesData.entries()) {
      // Remove data older than 10 minutes
      if (now - data.timestamp > 10 * 60 * 1000) {
        expiredKeys.push(key);
      }
    }

    expiredKeys.forEach(key => {
      global.skippedRolesData.delete(key);
      console.log(`Cleaned up expired skipped roles data: ${key}`);
    });
  }
}, 10 * 60 * 1000); // Run every 10 minutes

// Login with your token
console.log('Attempting to log in...');
client.login(config.token).then(() => {
  console.log('Login successful!');
}).catch(error => {
  console.error('Login failed:', error);
});

// Export the client for potential use in other files
module.exports = { client };
