const {
    <PERSON><PERSON><PERSON><PERSON>mandB<PERSON>er,
    ActionRowBuilder,
    StringSelectMenuBuilder,
    StringSelectMenuOptionBuilder,
    UserSelectMenuBuilder,
    ModalBuilder,
    TextInputBuilder,
    TextInputStyle,
    PermissionFlagsBits,
    MessageFlags,
    ButtonBuilder,
    ButtonStyle
} = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('bulkmanager')
        .setDescription('Manage bulk operations for roles and channels')
        .setDefaultMemberPermissions(PermissionFlagsBits.ManageRoles | PermissionFlagsBits.ManageChannels),

    async execute(interaction) {
        try {
            const result = await this.showMainMenu(interaction);
            if (!result) {
                console.log('Failed to show main menu in execute method');
                if (!interaction.replied && !interaction.deferred) {
                    await interaction.reply({
                        content: 'An error occurred while showing the bulk manager menu. Please try again.',
                        flags: MessageFlags.Ephemeral
                    });
                }
            }
        } catch (error) {
            console.error('Error in bulkManager execute method:', error);
            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({
                    content: `An error occurred: ${error.message}`,
                    flags: MessageFlags.Ephemeral
                });
            }
        }
    },

    async showMainMenu(interaction, isFollowUp = false) {
        try {
            // Create the select menu for operation type
            const selectMenu = new StringSelectMenuBuilder()
                .setCustomId('bulkManagerSelect')
                .setPlaceholder('Select an operation')
                .addOptions(
                    // Role management options
                    new StringSelectMenuOptionBuilder()
                        .setLabel('Remove Roles from All Members')
                        .setDescription('Remove roles from all members without deleting the roles')
                        .setValue('bulkRemoveRoles')
                        .setEmoji('🧹'),
                    new StringSelectMenuOptionBuilder()
                        .setLabel('Bulk User Role Management')
                        .setDescription('Add/remove roles to/from multiple users at once')
                        .setValue('bulkUserRoleManagement')
                        .setEmoji('👥'),
                    // Removed individual user role management options in favor of the bulk option
                    new StringSelectMenuOptionBuilder()
                        .setLabel('Delete Roles')
                        .setDescription('Delete multiple roles from the server')
                        .setValue('bulkDeleteRoles')
                        .setEmoji('❌'),
                    new StringSelectMenuOptionBuilder()
                        .setLabel('Delete Roles by Pattern')
                        .setDescription('Delete roles matching a pattern')
                        .setValue('bulkDeleteRolesByPattern')
                        .setEmoji('🔍'),

                    // Channel management options
                    new StringSelectMenuOptionBuilder()
                        .setLabel('Delete Channels')
                        .setDescription('Delete multiple channels at once')
                        .setValue('bulkDeleteChannels')
                        .setEmoji('📝'),
                    new StringSelectMenuOptionBuilder()
                        .setLabel('Delete Channels by Category')
                        .setDescription('Delete all channels in a category')
                        .setValue('bulkDeleteChannelsByCategory')
                        .setEmoji('📁'),
                    new StringSelectMenuOptionBuilder()
                        .setLabel('Delete Channels by Pattern')
                        .setDescription('Delete channels matching a pattern')
                        .setValue('bulkDeleteChannelsByPattern')
                        .setEmoji('🔎'),

                    // Channel creation options
                    new StringSelectMenuOptionBuilder()
                        .setLabel('Create Channels')
                        .setDescription('Create multiple channels at once')
                        .setValue('createChannels')
                        .setEmoji('➕'),
                    new StringSelectMenuOptionBuilder()
                        .setLabel('Create Private Channels')
                        .setDescription('Create private channels with matching roles')
                        .setValue('createPrivateChannels')
                        .setEmoji('🔒'),

                    // Role creation options
                    new StringSelectMenuOptionBuilder()
                        .setLabel('Create Roles')
                        .setDescription('Create multiple roles at once')
                        .setValue('createRoles')
                        .setEmoji('✨')
                );

            // Create and send the action row with the select menu
            const row = new ActionRowBuilder().addComponents(selectMenu);

            // Create a button to refresh the menu
            const refreshButton = new ButtonBuilder()
                .setCustomId('refreshBulkManagerMenu')
                .setLabel('Refresh Menu')
                .setStyle(ButtonStyle.Secondary)
                .setEmoji('🔄');

            const buttonRow = new ActionRowBuilder().addComponents(refreshButton);

            const content = 'Select a bulk management operation:';
            const components = [row, buttonRow];
            const flags = MessageFlags.Ephemeral;

            try {
                if (isFollowUp) {
                    if (interaction.replied || interaction.deferred) {
                        await interaction.followUp({
                            content,
                            components,
                            flags
                        });
                    } else {
                        await interaction.update({
                            content,
                            components,
                            flags
                        });
                    }
                } else {
                    await interaction.reply({
                        content,
                        components,
                        flags
                    });
                }
                return true;
            } catch (responseError) {
                console.error('Error responding to interaction in showMainMenu:', responseError);

                // Try a fallback response if the primary response fails
                try {
                    if (!interaction.replied && !interaction.deferred) {
                        await interaction.reply({
                            content: 'An error occurred while showing the menu. Please try again.',
                            flags: MessageFlags.Ephemeral
                        });
                    } else if (interaction.replied) {
                        await interaction.followUp({
                            content: 'An error occurred while showing the menu. Please try again.',
                            flags: MessageFlags.Ephemeral
                        });
                    }
                } catch (fallbackError) {
                    console.error('Error sending fallback response in showMainMenu:', fallbackError);
                }
                return false;
            }
        } catch (error) {
            console.error('Error in showMainMenu:', error);
            return false;
        }
    },

    // Helper methods for creating modals
    createBulkRemoveRolesModal() {
        const modal = new ModalBuilder()
            .setCustomId('bulkRemoveRolesModal')
            .setTitle('Bulk Role Removal');

        const roleNamesInput = new TextInputBuilder()
            .setCustomId('roleNamesInput')
            .setLabel('Role Names (one per line)')
            .setStyle(TextInputStyle.Paragraph)
            .setPlaceholder('Moderator\nMember\nGuest')
            .setRequired(true);

        const firstActionRow = new ActionRowBuilder().addComponents(roleNamesInput);
        modal.addComponents(firstActionRow);

        return modal;
    },

    createBulkRemoveUserRolesModal(userId, username) {
        const modal = new ModalBuilder()
            .setCustomId(`bulkRemoveUserRolesModal-${userId}`)
            .setTitle(`Remove Roles from ${username}`);

        const roleNamesInput = new TextInputBuilder()
            .setCustomId('roleNamesInput')
            .setLabel('Role Names (one per line)')
            .setStyle(TextInputStyle.Paragraph)
            .setPlaceholder('Moderator\nMember\nGuest')
            .setRequired(true);

        const firstActionRow = new ActionRowBuilder().addComponents(roleNamesInput);
        modal.addComponents(firstActionRow);

        return modal;
    },

    createBulkRemoveUserRolesByIdModal(userId, username) {
        const modal = new ModalBuilder()
            .setCustomId(`bulkRemoveUserRolesByIdModal-${userId}`)
            .setTitle(`Remove Roles from ${username}`);

        const roleIdsInput = new TextInputBuilder()
            .setCustomId('roleIdsInput')
            .setLabel('Role IDs (one per line)')
            .setStyle(TextInputStyle.Paragraph)
            .setPlaceholder('123456789012345678\n234567890123456789\n345678901234567890')
            .setRequired(true);

        const firstActionRow = new ActionRowBuilder().addComponents(roleIdsInput);
        modal.addComponents(firstActionRow);

        return modal;
    },

    createBulkDeleteRolesModal() {
        const modal = new ModalBuilder()
            .setCustomId('bulkDeleteRolesModal')
            .setTitle('Bulk Role Deletion');

        const roleNamesInput = new TextInputBuilder()
            .setCustomId('roleNamesInput')
            .setLabel('Role Names (one per line)')
            .setStyle(TextInputStyle.Paragraph)
            .setPlaceholder('Moderator\nMember\nGuest')
            .setRequired(true);

        const firstActionRow = new ActionRowBuilder().addComponents(roleNamesInput);
        modal.addComponents(firstActionRow);

        return modal;
    },

    createBulkDeleteRolesByPatternModal() {
        const modal = new ModalBuilder()
            .setCustomId('bulkDeleteRolesByPatternModal')
            .setTitle('Bulk Role Deletion by Pattern');

        const patternInput = new TextInputBuilder()
            .setCustomId('patternInput')
            .setLabel('Role Name Pattern')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('e.g. "Team-" to match all roles starting with "Team-"')
            .setRequired(true);

        const confirmationInput = new TextInputBuilder()
            .setCustomId('confirmationInput')
            .setLabel('Type "confirm" to proceed')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('confirm')
            .setRequired(true);

        const firstActionRow = new ActionRowBuilder().addComponents(patternInput);
        const secondActionRow = new ActionRowBuilder().addComponents(confirmationInput);

        modal.addComponents(firstActionRow, secondActionRow);

        return modal;
    },

    createBulkDeleteChannelsModal() {
        const modal = new ModalBuilder()
            .setCustomId('bulkDeleteChannelsModal')
            .setTitle('Bulk Channel Deletion');

        const channelNamesInput = new TextInputBuilder()
            .setCustomId('channelNamesInput')
            .setLabel('Channel Names (one per line)')
            .setStyle(TextInputStyle.Paragraph)
            .setPlaceholder('general\nannouncements\noff-topic')
            .setRequired(true);

        const firstActionRow = new ActionRowBuilder().addComponents(channelNamesInput);
        modal.addComponents(firstActionRow);

        return modal;
    },

    createBulkDeleteChannelsByCategoryModal() {
        const modal = new ModalBuilder()
            .setCustomId('bulkDeleteChannelsByCategoryModal')
            .setTitle('Bulk Channel Deletion by Category');

        const categoryNameInput = new TextInputBuilder()
            .setCustomId('categoryNameInput')
            .setLabel('Category Name')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('Enter the exact category name')
            .setRequired(true);

        const deleteCategory = new TextInputBuilder()
            .setCustomId('deleteCategoryInput')
            .setLabel('Delete the category itself?')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('yes/no (default: no)')
            .setRequired(false);

        const firstActionRow = new ActionRowBuilder().addComponents(categoryNameInput);
        const secondActionRow = new ActionRowBuilder().addComponents(deleteCategory);

        modal.addComponents(firstActionRow, secondActionRow);

        return modal;
    },

    createBulkDeleteChannelsByPatternModal() {
        const modal = new ModalBuilder()
            .setCustomId('bulkDeleteChannelsByPatternModal')
            .setTitle('Bulk Channel Deletion by Pattern');

        const patternInput = new TextInputBuilder()
            .setCustomId('patternInput')
            .setLabel('Channel Name Pattern')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('e.g. "team-" to match all channels starting with "team-"')
            .setRequired(true);

        const confirmationInput = new TextInputBuilder()
            .setCustomId('confirmationInput')
            .setLabel('Type "confirm" to proceed')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('confirm')
            .setRequired(true);

        const firstActionRow = new ActionRowBuilder().addComponents(patternInput);
        const secondActionRow = new ActionRowBuilder().addComponents(confirmationInput);

        modal.addComponents(firstActionRow, secondActionRow);

        return modal;
    },

    createChannelsModal() {
        const modal = new ModalBuilder()
            .setCustomId('createChannelsModal')
            .setTitle('Enhanced Channel Creation');

        const channelNamesInput = new TextInputBuilder()
            .setCustomId('channelNamesInput')
            .setLabel('Channel Names (one per line)')
            .setStyle(TextInputStyle.Paragraph)
            .setPlaceholder('general\nannouncements\noff-topic')
            .setRequired(true);

        const channelTypeInput = new TextInputBuilder()
            .setCustomId('channelTypeInput')
            .setLabel('Channel Type (text/voice)')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('text')
            .setRequired(true);

        const categoryInput = new TextInputBuilder()
            .setCustomId('categoryInput')
            .setLabel('Category Name (optional)')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('Leave blank for no category')
            .setRequired(false);

        const roleCreationInput = new TextInputBuilder()
            .setCustomId('roleCreationInput')
            .setLabel('Create Roles? (yes/no/selective)')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('yes = all channels, no = none, selective = choose per channel')
            .setValue('no')
            .setRequired(true);

        const customRoleNamesInput = new TextInputBuilder()
            .setCustomId('customRoleNamesInput')
            .setLabel('Custom Role Names (optional, one per line)')
            .setStyle(TextInputStyle.Paragraph)
            .setPlaceholder('Leave blank to use channel names\ngeneral-members\nannouncement-viewers\noff-topic-users')
            .setRequired(false);

        const firstActionRow = new ActionRowBuilder().addComponents(channelNamesInput);
        const secondActionRow = new ActionRowBuilder().addComponents(channelTypeInput);
        const thirdActionRow = new ActionRowBuilder().addComponents(categoryInput);
        const fourthActionRow = new ActionRowBuilder().addComponents(roleCreationInput);
        const fifthActionRow = new ActionRowBuilder().addComponents(customRoleNamesInput);

        modal.addComponents(firstActionRow, secondActionRow, thirdActionRow, fourthActionRow, fifthActionRow);

        return modal;
    },

    createPrivateChannelsModal() {
        const modal = new ModalBuilder()
            .setCustomId('createPrivateChannelsModal')
            .setTitle('Bulk Private Channel Creation');

        const channelNamesInput = new TextInputBuilder()
            .setCustomId('channelNamesInput')
            .setLabel('Channel Names (one per line)')
            .setStyle(TextInputStyle.Paragraph)
            .setPlaceholder('team-alpha\nteam-beta\nteam-gamma')
            .setRequired(true);

        const channelTypeInput = new TextInputBuilder()
            .setCustomId('channelTypeInput')
            .setLabel('Channel Type (text/voice)')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('text')
            .setRequired(true);

        const categoryInput = new TextInputBuilder()
            .setCustomId('categoryInput')
            .setLabel('Category Name (optional)')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('Leave blank for no category')
            .setRequired(false);

        const roleColorInput = new TextInputBuilder()
            .setCustomId('roleColorInput')
            .setLabel('Role Color (hex code, optional)')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('#FF0000')
            .setRequired(false);

        const firstActionRow = new ActionRowBuilder().addComponents(channelNamesInput);
        const secondActionRow = new ActionRowBuilder().addComponents(channelTypeInput);
        const thirdActionRow = new ActionRowBuilder().addComponents(categoryInput);
        const fourthActionRow = new ActionRowBuilder().addComponents(roleColorInput);

        modal.addComponents(firstActionRow, secondActionRow, thirdActionRow, fourthActionRow);

        return modal;
    },

    createRolesModal() {
        const modal = new ModalBuilder()
            .setCustomId('createRolesModal')
            .setTitle('Bulk Role Creation');

        const roleNamesInput = new TextInputBuilder()
            .setCustomId('roleNamesInput')
            .setLabel('Role Names (one per line)')
            .setStyle(TextInputStyle.Paragraph)
            .setPlaceholder('Moderator\nMember\nGuest')
            .setRequired(true);

        const roleColorInput = new TextInputBuilder()
            .setCustomId('roleColorInput')
            .setLabel('Role Color (hex code, optional)')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('#FF0000')
            .setRequired(false);

        const roleVisibilityInput = new TextInputBuilder()
            .setCustomId('roleVisibilityInput')
            .setLabel('Display role separately? (yes/no)')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('no')
            .setValue('no')
            .setRequired(true);

        const firstActionRow = new ActionRowBuilder().addComponents(roleNamesInput);
        const secondActionRow = new ActionRowBuilder().addComponents(roleColorInput);
        const thirdActionRow = new ActionRowBuilder().addComponents(roleVisibilityInput);

        modal.addComponents(firstActionRow, secondActionRow, thirdActionRow);

        return modal;
    },

    createBulkUserRoleManagementModal() {
        const modal = new ModalBuilder()
            .setCustomId('bulkUserRoleManagementModal')
            .setTitle('Bulk User Role Management');

        const userNamesInput = new TextInputBuilder()
            .setCustomId('userNamesInput')
            .setLabel('User Names or IDs (one per line)')
            .setStyle(TextInputStyle.Paragraph)
            .setPlaceholder('user1\nuser2\n123456789012345678')
            .setRequired(true);

        const roleNamesInput = new TextInputBuilder()
            .setCustomId('roleNamesInput')
            .setLabel('Role Names or IDs (one per line)')
            .setStyle(TextInputStyle.Paragraph)
            .setPlaceholder('Role1\nRole2\n987654321098765432')
            .setRequired(true);

        const actionTypeInput = new TextInputBuilder()
            .setCustomId('actionTypeInput')
            .setLabel('Action (add/remove)')
            .setStyle(TextInputStyle.Short)
            .setPlaceholder('remove')
            .setValue('remove')
            .setRequired(true);

        const firstActionRow = new ActionRowBuilder().addComponents(userNamesInput);
        const secondActionRow = new ActionRowBuilder().addComponents(roleNamesInput);
        const thirdActionRow = new ActionRowBuilder().addComponents(actionTypeInput);

        modal.addComponents(firstActionRow, secondActionRow, thirdActionRow);

        return modal;
    },

    // Helper function for fuzzy role matching
    findSimilarRole(guild, targetRoleName) {
        const targetFirstWord = targetRoleName.toLowerCase().split(/[-_\s]/)[0];

        // First, try exact match
        const exactMatch = guild.roles.cache.find(role =>
            role.name.toLowerCase() === targetRoleName.toLowerCase()
        );
        if (exactMatch) {
            return { role: exactMatch, matchType: 'exact' };
        }

        // Then try fuzzy matching based on first word
        const fuzzyMatch = guild.roles.cache.find(role => {
            const roleFirstWord = role.name.toLowerCase().split(/[-_\s]/)[0];
            return roleFirstWord === targetFirstWord && role.name !== '@everyone';
        });

        if (fuzzyMatch) {
            return { role: fuzzyMatch, matchType: 'fuzzy' };
        }

        return null;
    },

    // Helper function to parse selective role creation input
    parseSelectiveRoleCreation(channelNames, selectiveInput) {
        const channelRoleMap = new Map();

        if (!selectiveInput || selectiveInput.trim() === '') {
            // If no selective input, default to no roles for any channel
            channelNames.forEach(name => {
                if (name.trim()) {
                    channelRoleMap.set(name.trim().toLowerCase(), false);
                }
            });
            return channelRoleMap;
        }

        // Parse selective input format: "channel1:yes,channel2:no,channel3:yes"
        const selections = selectiveInput.split(',').map(s => s.trim());

        // Initialize all channels to false
        channelNames.forEach(name => {
            if (name.trim()) {
                channelRoleMap.set(name.trim().toLowerCase(), false);
            }
        });

        // Apply selective choices
        selections.forEach(selection => {
            const [channelName, shouldCreate] = selection.split(':').map(s => s.trim());
            if (channelName && shouldCreate) {
                const normalizedChannelName = channelName.toLowerCase();
                if (channelRoleMap.has(normalizedChannelName)) {
                    channelRoleMap.set(normalizedChannelName, shouldCreate.toLowerCase() === 'yes');
                }
            }
        });

        return channelRoleMap;
    }
};
